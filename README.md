# SmartOCR.lk - Intelligent Document Processing SaaS

A comprehensive OCR (Optical Character Recognition) SaaS platform specifically designed for Sri Lankan documents, built with Laravel 11 and modern web technologies.

## 🚀 Features

### Core Functionality
- **Multi-Document Support**: Process NICs, driving licenses, vehicle registrations, land deeds, and invoices
- **High Accuracy**: Optimized for Sri Lankan document formats with 95%+ accuracy
- **Real-time Processing**: Fast document processing with live status updates
- **Secure Processing**: Documents auto-deleted after 24 hours for privacy compliance


## 🛠 Technology Stack

- **Backend**: Laravel 11 with PHP 8.2+
- **Frontend**: Livewire + Tailwind CSS + Alpine.js
- **Database**: MySQL 8.0+
- **Authentication**: Laravel Jetstream with Sanctum
- **File Storage**: Local/Cloud storage support

## 📋 Prerequisites

- PHP 8.2 or higher
- Composer 2.x
- Node.js 18+ and npm
- MySQL 8.0+
- Redis (recommended for queues and caching)
- Web server (Nginx/Apache)


**Built with ❤️ for Sri Lankan businesses**
